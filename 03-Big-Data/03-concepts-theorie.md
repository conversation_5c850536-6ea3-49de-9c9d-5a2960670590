# 🧠 Concepts & Théorie

## Slide
**⚙️ CONCEPTS CLÉS**
- 📊 5V Model
- 🏗️ Data Lake
- 🔄 ETL/ELT

<!-- 
SPEECH (1,5 minutes) :
"Trois concepts structurent l'univers Big Data. Permettez-moi de les démystifier avec des analogies parlantes."

LES 5V DU BIG DATA :
"Le Big Data se définit par 5 caractéristiques, les 5V. Volume : imaginez la bibliothèque d'Alexandrie multipliée par un million. Vitesse : les données arrivent comme un torrent, pas comme un robinet. Variété : texte, images, vidéos, capteurs... comme un bazar géant. Véracité : toutes les données ne sont pas fiables, il faut trier le bon grain de l'ivraie. Valeur : l'objectif final, transformer ces données en insights business."

DATA LAKE :
"Un data lake, c'est comme un immense réservoir naturel qui accueille toutes les eaux : rivières, pluie, sources souterraines. Contrairement à un data warehouse (piscine municipale avec ses lignes bien définies), le data lake stocke tout en format natif. On structure après, selon les besoins d'analyse."

ETL VS ELT :
"ETL, c'est comme préparer un plat : on épluche (Extract), on cuisine (Transform), puis on sert (Load). ELT, c'est l'inverse : on met tout au frigo (Load), et on cuisine à la demande (Transform). Avec le cloud et la puissance de calcul, ELT devient plus flexible."
-->

## Concepts fondamentaux

### 📊 Les 5V du Big Data
**Définition** : Modèle caractérisant les défis et opportunités du Big Data selon 5 dimensions.

**Vulgarisation** : Comme caractériser un torrent par son débit, sa source, sa pureté, sa direction et son utilité.

**Les 5 dimensions** :
- **Volume** : Téraoctets à pétaoctets (bibliothèque d'Alexandrie x1M)
- **Vitesse** : Temps réel à quasi-temps réel (torrent vs robinet)
- **Variété** : Structuré, semi-structuré, non-structuré (bazar géant)
- **Véracité** : Qualité et fiabilité des données (trier le bon grain)
- **Valeur** : ROI et insights business (objectif final)

### 🏗️ Data Lake vs Data Warehouse
**Data Lake** : Stockage centralisé de données brutes dans leur format natif.

**Vulgarisation** : Réservoir naturel accueillant toutes les eaux (rivières, pluie, sources) vs piscine municipale avec lignes bien définies.

**Avantages Data Lake** :
- Flexibilité : stockage de tout type de données
- Coût : stockage moins cher que data warehouse
- Agilité : schema-on-read vs schema-on-write

**Défis** :
- Gouvernance des données
- Risque de "data swamp" (marécage de données)

### 🔄 ETL vs ELT
**ETL (Extract, Transform, Load)** : Processus traditionnel de traitement des données.

**Vulgarisation** : Comme préparer un plat : éplucher → cuisiner → servir.

**ELT (Extract, Load, Transform)** : Approche moderne privilégiant la flexibilité.

**Vulgarisation** : Mettre tout au frigo → cuisiner à la demande selon les goûts.

**Évolution** :
- **ETL** : Adapté aux data warehouses, transformations prédéfinies
- **ELT** : Exploite la puissance cloud, transformations à la demande
- **Tendance** : ELT gagne avec l'essor du cloud et des data lakes
