# 1. Big Data : fiabilité et validité des données

## 1. Contexte (≈ 3 minutes)

- **Définition et importance**  
  - Le Big Data se caractérise par un volume massif de données (Volume, Vélocité, Variété).  
  - Pour l’entreprise, la fiabilité et la validité de ces données sont essentielles afin d’orienter des décisions stratégiques précises.

- **Évolution et historique**  
  - L’émergence d’outils d’analyse avancés (IA, algorithmes prédictifs) a renforcé la nécessité de disposer de données de qualité.  
  - Des investissements croissants en infrastructure (stockage, solutions cloud) soulignent l’importance stratégique de la donnée.

- **Tendances actuelles**  
  - Multiplication des sources de données (IoT, réseaux sociaux, applications métiers).  
  - Prise de conscience accrue des dirigeants sur les risques de “mauvaise” data : coûts, erreurs de pilotage, perte de confiance.

## 2. Enjeux dégagés (≈ 1 minute)

- **Technologique**  
  > *Comment s’assurer que les outils et processus garantissent une qualité et une traçabilité des données suffisantes ?*

- **Économique**  
  > *Quel est l’impact financier d’une mauvaise qualité de données (perte de productivité, erreurs de décision) et comment y remédier ?*

- **Légal**  
  > *Quelles obligations réglementaires imposent un certain niveau de fiabilité et de validité (normes ISO, RGPD) ?*
