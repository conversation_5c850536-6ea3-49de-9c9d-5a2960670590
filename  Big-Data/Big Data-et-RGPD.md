# Big Data et RGPD

## 1. Présentation du contexte (≈ 3 minutes)

### Définition et importance
Le RGPD — Règlement Général sur la Protection des Données — est une réglementation européenne entrée en vigueur le 25 mai 2018.  
Son objectif est de protéger les données personnelles des citoyens européens face à l’usage croissant de ces données par les entreprises, administrations ou plateformes numériques.

Dans un monde dominé par le **Big Data**, où les données sont collectées en masse pour améliorer les services, anticiper les comportements ou optimiser les stratégies commerciales, le RGPD impose un **cadre juridique strict**.

### Évolution et historique
Avant le RGPD, la protection des données reposait sur des lois nationales inégalement appliquées.  
Le RGPD a unifié les règles, renforcé les droits des individus, et introduit des **sanctions financières importantes** pouvant atteindre jusqu’à 4% du chiffre d’affaires mondial d’une entreprise.

### Tendances actuelles
- Les entreprises investissent désormais dans des outils et des équipes dédiées à la conformité RGPD.
- On observe une montée en puissance des métiers liés à la **Data Governance** (DPO, Data Steward…).
- En parallèle, d’autres régions du monde s’en inspirent, comme la **CCPA** en Californie.

> 🎯 En résumé, dans un contexte de surabondance de données, le RGPD oblige les organisations à revoir la manière dont elles collectent, stockent et exploitent la donnée.

---

## 2. Enjeux dégagés (≈ 1 minute)

### Trois axes PESTEL

1. **Légal**  
   > *Comment s’assurer que chaque étape du cycle de vie des données respecte le RGPD (du recueil au traitement jusqu’à la suppression) ?*

2. **Économique**  
   > *Quel est le coût réel de la mise en conformité RGPD ? Est-ce une contrainte ou une opportunité stratégique à long terme ?*

3. **Technologique**  
   > *Quelles solutions permettent de concilier performance d’analyse des données et respect des droits des utilisateurs (anonymisation, gouvernance, consentement granulaire) ?*

---

## 3. Présentation et analyse des concepts (≈ 6 minutes)

### Le RGPD en bref

Le RGPD repose sur **7 grands principes**, essentiels à vulgariser :

1. **Licéité, loyauté et transparence**  
   → Informer clairement les personnes concernées et obtenir leur consentement.
2. **Limitation des finalités**  
   → Utiliser les données uniquement pour un objectif défini dès le départ.
3. **Minimisation des données**  
   → Ne collecter que les données strictement nécessaires.
4. **Exactitude**  
   → Maintenir les données à jour et les corriger si besoin.
5. **Limitation de conservation**  
   → Conserver les données pour une durée limitée et justifiée.
6. **Intégrité et confidentialité**  
   → Sécuriser les données contre tout accès non autorisé ou toute perte.
7. **Responsabilité (accountability)**  
   → L’entreprise doit être en mesure de démontrer sa conformité.

### Définitions clés
- **Données personnelles** : toute information permettant d’identifier une personne (nom, e-mail, IP...).
- **Responsable de traitement** : décide des finalités et des moyens du traitement.
- **Sous-traitant** : agit pour le compte du responsable.
- **DPO** : garant du respect du RGPD dans l’entreprise.

### Tensions RGPD vs Big Data
- Le Big Data repose sur des volumes énormes de données souvent croisées, parfois sans finalité initiale définie.
- La notion de “minimisation” est difficilement applicable lorsque l’objectif est de détecter des corrélations imprévues.
- L’utilisation de l’intelligence artificielle ajoute de la complexité au suivi des traitements de données.

### Solutions pour concilier les deux
- **Privacy by Design & by Default** : penser à la protection des données dès la conception.
- **Pseudonymisation et anonymisation** : techniques pour limiter le risque tout en permettant l’analyse.
- **Portails de gestion des consentements** : pour renforcer la transparence et les droits des utilisateurs.

---

## 4. Benchmark (≈ 6 minutes)

### Objectif
Montrer comment des entreprises ont intégré le RGPD dans un contexte Big Data, à travers différents secteurs.

---

#### 🏦 Secteur bancaire – BNP Paribas

- Plateforme centralisée de gestion des consentements.
- Suivi automatique de la traçabilité (logs, audits réguliers).
- Formation obligatoire pour tous les collaborateurs.

> ✅ Résultat : conformité intégrée au cycle projet, maîtrise des risques juridiques.

---

#### 🛒 Secteur e-commerce – Cdiscount

- Utilisation de données comportementales pour personnalisation.
- Mise à disposition d’un **“privacy dashboard”** pour que les utilisateurs gèrent eux-mêmes leurs préférences.
- Suppression automatique des données inactives.

> ✅ Résultat : plus grande transparence, baisse des réclamations clients.

---

#### 🏥 Secteur santé – Doctolib

- Hébergement certifié HDS (données médicales).
- DPO intégré à l’équipe produit.
- Processus clair pour l’effacement ou l’accès aux données (traitement sous 72h).

> ✅ Résultat : conformité renforcée, image éthique, confiance des patients.

---

### Facteurs clés de succès

| Facteur | Description |
|--------|-------------|
| 🎓 Culture RGPD | Formations, implication de toutes les équipes. |
| 🧭 Gouvernance claire | DPO identifié, responsabilités définies. |
| 🛠️ Outils adaptés | Audit, chiffrement, portails utilisateurs. |
| 📊 Suivi continu | Indicateurs de conformité, revues régulières. |

---

## 5. Réponse stratégique aux enjeux (≈ 1 minute)

### Plan d’action proposé

1. **Audit & cartographie**  
   - Identifier toutes les données collectées et les points de traitement.

2. **Mise en conformité technique**  
   - Implémenter des outils de consentement, pseudonymisation, contrôle d’accès.

3. **Gouvernance interne**  
   - Désigner un DPO, former les collaborateurs, mettre en place une politique claire.

4. **Suivi et amélioration continue**  
   - Mettre en place des KPIs (ex : délai de réponse aux demandes RGPD) et des audits réguliers.

> 🎯 Cette approche permet de répondre aux enjeux identifiés : conformité légale, maîtrise des risques économiques et performance technique durable.
