# ❓ Questions Jury

## Slide
**🎯 QUESTIONS PIÈGES**
- 🔥 Techniques
- 💼 Coûts
- 🚀 Futur

<!-- 
SPEECH (1,5 minutes) :
"Anticipons les questions pièges sur le cloud. Voici mes réponses préparées pour les trois types de questions classiques."

QUESTION TECHNIQUE PIÈGE :
"'Le cloud est-il vraiment plus sécurisé que l'on-premise ?'
Ma réponse : 'C'est un modèle de responsabilité partagée. AWS sécurise l'infrastructure, nous sécurisons nos applications. Les cloud providers investissent des milliards en sécurité, mais une mauvaise configuration peut créer des failles. La sécurité cloud dépend de l'expertise de l'équipe qui l'implémente.'"

QUESTION BUSINESS PIÈGE :
"'Comment justifier une migration cloud qui coûte plus cher la première année ?'
Ma réponse : 'C'est un investissement, pas un coût. Comme rénover une usine : cher au début, mais gains de productivité durables. Le cloud apporte agilité, innovation, scalabilité. Le ROI se mesure sur 3-5 ans, pas sur la première facture.'"

QUESTION PROSPECTIVE PIÈGE :
"'Le edge computing va-t-il remplacer le cloud centralisé ?'
Ma réponse : 'Complémentaire, pas concurrent. Edge pour la latence critique (IoT, gaming), cloud centralisé pour le compute intensif et le stockage. C'est un continuum : device → edge → cloud selon les besoins.'"
-->

## Questions Pièges & Réponses Préparées

### 🔥 Questions Techniques

**Q1 : "Le cloud est-il vraiment plus sécurisé que l'on-premise ?"**

**Réponse structurée** :
- **Modèle partagé** : "Responsabilité partagée : AWS sécurise l'infrastructure, nous sécurisons nos applications"
- **Investissement** : "Les cloud providers investissent des milliards en sécurité physique et logique"
- **Nuance** : "Mais une mauvaise configuration peut créer des failles. La sécurité dépend de l'expertise d'implémentation"

**Q2 : "Pourquoi pas tout en serverless si c'est si efficace ?"**

**Réponse structurée** :
- **Cas d'usage** : "Serverless excellent pour workloads événementiels, moins pour applications stateful"
- **Limites** : "Cold start, vendor lock-in, debugging complexe"
- **Pragmatisme** : "Chaque architecture a son contexte optimal"

### 💼 Questions Business

**Q3 : "Comment justifier une migration cloud qui coûte plus cher la première année ?"**

**Réponse structurée** :
- **Investissement** : "C'est un investissement, pas un coût. Comme rénover une usine"
- **Bénéfices** : "Agilité, innovation, scalabilité ne se mesurent pas qu'en euros"
- **Timeline** : "ROI sur 3-5 ans, pas sur la première facture"

**Q4 : "Comment éviter le vendor lock-in ?"**

**Réponse structurée** :
- **Stratégie** : "Architecture cloud-agnostic, containers, APIs standards"
- **Réalisme** : "Lock-in partiel inévitable pour bénéficier des services managés"
- **Équilibre** : "Arbitrage entre portabilité et optimisation"

### 🚀 Questions Prospectives

**Q5 : "Le edge computing va-t-il remplacer le cloud centralisé ?"**

**Réponse structurée** :
- **Complémentarité** : "Edge et cloud sont complémentaires, pas concurrents"
- **Cas d'usage** : "Edge pour latence critique, cloud pour compute intensif"
- **Continuum** : "Device → Edge → Cloud selon les besoins"

**Q6 : "L'IA va-t-elle automatiser la gestion cloud ?"**

**Réponse structurée** :
- **Évolution** : "L'IA optimise déjà (auto-scaling, anomaly detection)"
- **Limites** : "Décisions stratégiques et architecture restent humaines"
- **Futur** : "Assistance intelligente, pas remplacement total"
