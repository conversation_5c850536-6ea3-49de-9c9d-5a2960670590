# 🧠 Concepts & Théorie

## Slide
**⚙️ CONCEPTS CLÉS**
- 🧠 Deep Learning
- 🗣️ LLM
- 👁️ Computer Vision

<!-- 
SPEECH (1,5 minutes) :
"Trois concepts révolutionnent l'IA moderne. Permettez-moi de les démystifier avec des analogies parlantes."

DEEP LEARNING :
"Le deep learning, c'est comme apprendre à un enfant à reconnaître les chiens. Au début, on lui montre des milliers de photos en disant 'chien' ou 'pas chien'. Progressivement, son cerveau crée des connexions : oreilles pointues, queue qui remue, quatre pattes. Les réseaux de neurones artificiels fonctionnent pareil : des couches de neurones qui apprennent des patterns de plus en plus complexes."

LARGE LANGUAGE MODELS :
"Un LLM, c'est comme un étudiant qui aurait lu toute la bibliothèque d'Alexandrie et pourrait discuter de n'importe quel sujet. Il a ingéré des milliards de textes et appris les patterns du langage humain. Quand vous lui posez une question, il prédit le mot le plus probable suivant, puis le suivant, créant des réponses cohérentes."

COMPUTER VISION :
"La vision par ordinateur, c'est donner des yeux à une machine. Comme nous reconnaissons instantanément un visage dans une foule, l'IA peut identifier objets, personnes, émotions dans une image. Applications : diagnostic médical, voiture autonome, reconnaissance faciale."
-->

## Concepts fondamentaux

### 🧠 Deep Learning & Réseaux de Neurones
**Définition** : Algorithmes d'apprentissage basés sur des réseaux de neurones artificiels multicouches.

**Vulgarisation** : Comme apprendre à un enfant à reconnaître les chiens en lui montrant des milliers de photos. Le cerveau crée progressivement des connexions pour identifier les patterns.

**Architecture** :
- **Neurones artificiels** : Unités de calcul inspirées du cerveau
- **Couches** : Input → Hidden layers → Output
- **Apprentissage** : Ajustement des poids via rétropropagation
- **Activation** : Fonctions non-linéaires (ReLU, Sigmoid)

**Applications** :
- Vision par ordinateur
- Traitement du langage naturel
- Reconnaissance vocale
- Jeux (AlphaGo, OpenAI Five)

### 🗣️ Large Language Models (LLM)
**Définition** : Modèles de langage entraînés sur des corpus massifs de texte pour comprendre et générer du langage humain.

**Vulgarisation** : Étudiant ayant lu toute la bibliothèque d'Alexandrie et pouvant discuter de n'importe quel sujet en prédisant le mot suivant le plus probable.

**Fonctionnement** :
- **Entraînement** : Milliards de paramètres sur téraoctets de texte
- **Architecture** : Transformers et mécanisme d'attention
- **Génération** : Prédiction séquentielle de tokens
- **Fine-tuning** : Spécialisation sur tâches spécifiques

**Capacités émergentes** :
- Raisonnement logique
- Créativité et génération
- Traduction multilingue
- Code et programmation

### 👁️ Computer Vision
**Définition** : Domaine de l'IA permettant aux machines d'interpréter et comprendre le contenu visuel.

**Vulgarisation** : Donner des yeux à une machine pour qu'elle reconnaisse instantanément objets, personnes, émotions comme nous le faisons naturellement.

**Techniques principales** :
- **CNN** : Réseaux convolutionnels pour extraction de features
- **Object detection** : Localisation et classification d'objets
- **Segmentation** : Délimitation précise des éléments
- **Face recognition** : Identification biométrique

**Applications transformantes** :
- **Médical** : Diagnostic radiologique, détection cancers
- **Automobile** : Véhicules autonomes, ADAS
- **Sécurité** : Surveillance, contrôle d'accès
- **Retail** : Checkout automatique, analyse comportementale
