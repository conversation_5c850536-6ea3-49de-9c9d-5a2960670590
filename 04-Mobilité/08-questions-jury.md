# ❓ Questions Jury

## Slide
**🎯 QUESTIONS PIÈGES**
- 🔥 Techniques
- 💼 Stratégie
- 🚀 Futur

<!-- 
SPEECH (1,5 minutes) :
"Anticipons les questions pièges sur la mobilité. Voici mes réponses structurées pour les trois catégories principales."

QUESTION TECHNIQUE PIÈGE :
"'Native, cross-platform ou PWA : que choisir ?'
Ma réponse : 'Ça dépend du contexte. Native pour performance maximale et fonctionnalités avancées. Cross-platform pour équilibrer coût et qualité. PWA pour démarrer rapidement et tester le marché. La vraie question : quels sont vos contraintes de budget, timeline et exigences techniques ?'"

QUESTION STRATÉGIE PIÈGE :
"'Comment justifier le coût d'une app mobile vs un site responsive ?'
Ma réponse : 'L'app native génère 3x plus d'engagement qu'un site mobile. Pour un e-commerce, ça se traduit par +25% de conversion et +40% de lifetime value client. Le ROI se calcule : coût développement vs revenus supplémentaires générés. Souvent rentable en 12-18 mois.'"

QUESTION PROSPECTIVE PIÈGE :
"'Les PWA vont-elles remplacer les apps natives ?'
Ma réponse : 'Convergence plutôt que remplacement. Les PWA comblent l'écart avec le natif, mais certaines fonctionnalités restent exclusives (caméra avancée, NFC, etc.). L'avenir appartient aux approches hybrides : PWA pour la majorité, natif pour les cas spécifiques.'"
-->

## Questions Pièges & Réponses Préparées

### 🔥 Questions Techniques

**Q1 : "Native, cross-platform ou PWA : que choisir ?"**

**Réponse structurée** :
- **Contexte** : "Dépend des contraintes : budget, timeline, exigences techniques"
- **Native** : "Performance max, fonctionnalités avancées, coût élevé"
- **Cross-platform** : "Équilibre coût/qualité, 80% des cas d'usage"
- **PWA** : "Démarrage rapide, test marché, évolution possible"
- **Décision** : "Matrice de décision basée sur critères métier"

**Q2 : "Comment gérer la fragmentation Android ?"**

**Réponse structurée** :
- **Réalité** : "24 000+ modèles, versions OS multiples"
- **Stratégie** : "Cibler 80% du marché (versions récentes)"
- **Outils** : "Testing cloud (Firebase Test Lab), responsive design"
- **Pragmatisme** : "Accepter limitations sur anciens devices"

### 💼 Questions Stratégie

**Q3 : "Comment justifier le coût d'une app mobile vs site responsive ?"**

**Réponse structurée** :
- **Engagement** : "App native = 3x plus d'engagement que site mobile"
- **Conversion** : "+25% conversion, +40% lifetime value client"
- **ROI** : "Calcul coût développement vs revenus supplémentaires"
- **Timeline** : "Rentabilité généralement en 12-18 mois"

**Q4 : "Mobile-first : par où commencer ?"**

**Réponse structurée** :
- **Audit** : "Analyser performance mobile actuelle"
- **Quick wins** : "Optimiser vitesse, simplifier parcours"
- **Priorisation** : "Pages à fort trafic/conversion d'abord"
- **Mesure** : "KPIs mobile spécifiques, A/B testing"

### 🚀 Questions Prospectives

**Q5 : "Les PWA vont-elles remplacer les apps natives ?"**

**Réponse structurée** :
- **Convergence** : "Évolution vers hybridation, pas remplacement"
- **PWA** : "Comblent l'écart, mais limitations subsistent"
- **Native** : "Reste nécessaire pour fonctionnalités avancées"
- **Futur** : "Approche pragmatique : PWA + natif selon besoins"

**Q6 : "Impact de la 5G sur le développement mobile ?"**

**Réponse structurée** :
- **Opportunités** : "Latence réduite, bande passante élevée"
- **Nouveaux usages** : "AR/VR, streaming haute qualité, edge computing"
- **Défis** : "Couverture progressive, consommation batterie"
- **Stratégie** : "Progressive enhancement, fallback 4G"
