# 📚 Annexe Sources

## Slide
**📖 SOURCES MOBILE**
- 📊 Études
- 🏢 Plateformes
- 📰 Veille

<!-- 
SPEECH (30 secondes) :
"Ma présentation s'appuie sur des sources de référence du secteur mobile : études d'usage et de marché, documentation des plateformes mobiles, et veille technologique spécialisée. Cette diversité garantit une vision équilibrée entre innovation et réalité terrain."
-->

## Sources Principales

### 📊 Études & Rapports de Marché
- **App Annie State of Mobile** - Usage et tendances applications
- **Google Mobile Speed Report** - Performance et impact business
- **Statista Mobile Internet Usage** - Statistiques globales
- **Sensor Tower Mobile App Trends** - Téléchargements et revenus
- **Comscore Mobile Metrix** - Comportements utilisateurs

### 🏢 Sources Plateformes & Constructeurs
- **Apple Human Interface Guidelines** - Standards iOS
- **Google Material Design** - Principes Android
- **React Native Documentation** - Framework cross-platform
- **Flutter Documentation** - Google framework
- **PWA Documentation (Google)** - Progressive Web Apps

### 📰 Veille Technologique Mobile
- **Mobile Dev Weekly** - Newsletter développement mobile
- **Android Developers Blog** - Actualités Android
- **iOS Dev Weekly** - Newsletter développement iOS
- **Smashing Magazine Mobile** - UX/UI mobile
- **A List Apart Mobile** - Bonnes pratiques web mobile

### 📈 Données Performance & UX
- **Google PageSpeed Insights** - Métriques performance
- **Core Web Vitals** - Standards Google
- **Lighthouse** - Audit qualité web
- **GTmetrix Mobile** - Tests performance mobile
- **WebPageTest** - Analyse détaillée chargement

### 🎓 Sources Académiques & Recherche
- **ACM Digital Library** - Recherche HCI mobile
- **IEEE Mobile Computing** - Innovations techniques
- **CHI Conference Papers** - Interaction homme-machine
- **UX Research Mobile Studies** - Études comportementales
- **Nielsen Norman Group Mobile** - Recherche UX

### 🔍 Outils de Veille & Communautés
- **GitHub Mobile Trends** - Projets open source populaires
- **Stack Overflow Mobile Tags** - Questions développeurs
- **Reddit** : r/iOSProgramming, r/androiddev, r/reactnative
- **Twitter** : #MobileDev, #iOSDev, #AndroidDev
- **LinkedIn** : Groupes Mobile Development, UX Design

### 📊 Analytics & Métriques
- **Google Analytics Mobile** - Comportement utilisateurs
- **Firebase Analytics** - Métriques applications
- **Mixpanel Mobile** - Analytics événementiels
- **Amplitude** - Product analytics
- **App Store Connect Analytics** - Métriques iOS

### 🎤 Conférences & Événements
- **Google I/O** - Innovations Android/Web
- **Apple WWDC** - Nouveautés iOS/macOS
- **React Native EU** - Communauté React Native
- **Flutter Interact** - Événements Flutter
- **Mobile World Congress** - Industrie mobile

### 📱 Tests & Benchmarks
- **Device Atlas** - Fragmentation appareils
- **BrowserStack** - Tests multi-devices
- **Firebase Test Lab** - Tests automatisés
- **Perfecto Mobile** - Cloud testing
- **Sauce Labs** - Tests cross-platform

## Critères de Fiabilité
- ✅ Sources officielles plateformes (Apple, Google)
- ✅ Études longitudinales sur comportements utilisateurs
- ✅ Données récentes < 3 mois (secteur très évolutif)
- ✅ Métriques terrain vs projections marketing
- ✅ Diversité géographique et démographique des études
