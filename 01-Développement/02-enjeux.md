# ⚖️ Enjeux

## Slide
**🌍 ENJEUX PESTEL**
- 🏛️ Politique
- 💰 Économique  
- 🌱 Environnemental

<!-- 
SPEECH (2 minutes) :
"Les enjeux du développement logiciel s'articulent autour de 6 dimensions majeures selon l'analyse PESTEL. Chaque dimension révèle des défis spécifiques qui impactent directement la stratégie des organisations."

POLITIQUE :
- Souveraineté numérique et indépendance technologique
- Régulations sur la protection des données (RGPD, CCPA)
- Politiques d'innovation et soutien public au secteur tech

ÉCONOMIQUE :
- Transformation des modèles économiques (SaaS, plateformes)
- Investissements massifs en R&D et talent acquisition
- Disruption des secteurs traditionnels par le software

SOCIÉTAL :
- Fracture numérique et accessibilité des technologies
- Impact sur l'emploi : automatisation vs création de nouveaux métiers
- Éthique et responsabilité dans le développement d'applications

TECHNOLOGIQUE :
- Accélération de l'innovation (IA, quantum computing)
- Complexité croissante des architectures distribuées
- Sécurité et résilience des systèmes critiques

ENVIRONNEMENTAL :
- Empreinte carbone du numérique (4% des émissions mondiales)
- Green coding et optimisation énergétique
- Économie circulaire et durabilité des équipements

LÉGAL :
- Propriété intellectuelle et open source
- Responsabilité juridique des algorithmes
- Conformité réglementaire sectorielle (finance, santé)
"
-->

## Analyse PESTEL

### 🏛️ Politique
- Souveraineté numérique et indépendance technologique
- Régulations sur la protection des données (RGPD, CCPA)
- Politiques d'innovation et soutien public au secteur tech

### 💰 Économique
- Transformation des modèles économiques (SaaS, plateformes)
- Investissements massifs en R&D et talent acquisition
- Disruption des secteurs traditionnels par le software

### 👥 Sociétal
- Fracture numérique et accessibilité des technologies
- Impact sur l'emploi : automatisation vs création de nouveaux métiers
- Éthique et responsabilité dans le développement d'applications

### 🔬 Technologique
- Accélération de l'innovation (IA, quantum computing)
- Complexité croissante des architectures distribuées
- Sécurité et résilience des systèmes critiques

### 🌱 Environnemental
- Empreinte carbone du numérique (4% des émissions mondiales)
- Green coding et optimisation énergétique
- Économie circulaire et durabilité des équipements

### ⚖️ Légal
- Propriété intellectuelle et open source
- Responsabilité juridique des algorithmes
- Conformité réglementaire sectorielle (finance, santé)
